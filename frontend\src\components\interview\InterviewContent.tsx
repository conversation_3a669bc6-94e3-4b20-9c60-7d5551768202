import React, { useMemo, useCallback } from 'react';
import { Message } from '../../hooks/useInterviewSession';
import MessageBubble from './MessageBubble';

interface InterviewContentProps {
  messages: Message[];
  isListening: boolean;
  mode?: 'live' | 'mock';
  onStartInterview?: () => void;
  onSendMessage?: (message: string) => void;
  isInterviewStarted?: boolean;
}

// 🔥 使用React.memo优化InterviewContent组件
const InterviewContentComponent: React.FC<InterviewContentProps> = ({
  messages,
  isListening,
  mode = 'live',
  onStartInterview,
  onSendMessage,
  isInterviewStarted = false
}) => {
  const contentRef = React.useRef<HTMLDivElement>(null);

  // 🔥 使用useMemo稳定消息统计，避免每次渲染都重新计算
  const messageStats = useMemo(() => {
    const aiSuggestionMessages = messages.filter(m => m.type === 'ai-suggestion');
    const streamingMessages = messages.filter(m => m.type === 'ai-suggestion' && m.id.startsWith('ai-streaming-'));
    const finalMessages = messages.filter(m => m.type === 'ai-suggestion' && m.id.startsWith('ai-suggestion-final-'));

    return {
      totalMessages: messages.length,
      aiSuggestionCount: aiSuggestionMessages.length,
      streamingCount: streamingMessages.length,
      finalCount: finalMessages.length,
      messageTypes: messages.map(m => ({
        id: m.id,
        type: m.type,
        contentLength: m.content.length,
        isStreaming: m.id.startsWith('ai-streaming-'),
        isFinal: m.id.startsWith('ai-suggestion-final-')
      }))
    };
  }, [messages]);

  // 🔥 使用useCallback稳定滚动函数
  const scrollToBottom = useCallback(() => {
    if (contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, []);

  // 🔥 将useMemo移到组件顶层，符合Hooks规则
  const renderedMessages = useMemo(() => {
    console.log('🔄 Re-rendering message list, count:', messages.length);
    return messages.map((message) => (
      <MessageBubble key={message.id} message={message} mode={mode} />
    ));
  }, [messages, mode]);

  React.useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);



  return (
    <div 
      ref={contentRef}
      className="flex-1 p-6 overflow-y-auto space-y-6 bg-gray-50 max-h-[calc(100vh-140px)]"
    >
      {messages.length === 0 ? (
        <div className="flex items-center justify-center h-full">
          <div className="text-center max-w-md mx-auto">
            {mode === 'mock' ? (
              // AI模拟面试：直接显示等待状态，不显示开始按钮
              <>
                <div className="flex justify-center mb-6">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                    <div className="w-8 h-8 bg-blue-500 rounded-full animate-pulse"></div>
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-gray-800 mb-4">AI面试官正在准备问题...</h3>
                <p className="text-gray-600 mb-8 leading-relaxed">
                  请稍等片刻，AI面试官正在为您准备专业的面试问题。
                </p>
                <div className="flex justify-center">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                    <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                  </div>
                </div>
              </>
            ) : (
              // Live模式：显示开始按钮或等待状态
              <>
                {!isInterviewStarted && onStartInterview ? (
                  // Live模式的开始按钮
                  <>
                    <div className="flex justify-center mb-6">
                      <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                        <div className="w-8 h-8 bg-blue-500 rounded-full"></div>
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold text-gray-800 mb-4">准备开始面试</h3>
                    <p className="text-gray-600 mb-8 leading-relaxed">
                      AI面试官将为您提供专业的面试问题，请确保您的设备音频正常工作。
                    </p>
                    <button
                      onClick={onStartInterview}
                      className="w-full bg-gray-800 text-white py-3 rounded-lg text-base font-semibold hover:bg-gray-700 transition-all duration-200 shadow-sm active:scale-95 active:shadow-inner"
                    >
                      开始面试
                    </button>
                  </>
                ) : (
                  // Live模式的等待状态
                  <>
                    <div className="flex justify-center mb-4">
                      {isListening ? (
                        <div className="relative">
                          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                            <div className="w-8 h-8 bg-blue-500 rounded-full animate-pulse"></div>
                          </div>
                          <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full"></div>
                        </div>
                      ) : (
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                          <div className="w-8 h-8 bg-gray-300 rounded-full"></div>
                        </div>
                      )}
                    </div>
                    <p className="text-gray-500 text-lg">
                      {isListening
                        ? "面试君正在聆听您的会议..."
                        : "等待开始录音..."}
                    </p>
                  </>
                )}
              </>
            )}
          </div>
        </div>
      ) : (
        <>
          {/* 🔧 优化后的消息渲染日志（减少频率） */}
          {messageStats.totalMessages % 5 === 1 &&
            console.log('🎯 InterviewContent rendering messages (throttled):', messageStats)
          }
          {renderedMessages}
        </>
      )}
      {isListening && messages.length > 0 && mode !== 'mock' && (
        <div className="flex justify-center">
          <div className="flex space-x-2 items-center bg-blue-50 px-4 py-2 rounded-full">
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
            <span className="text-sm text-blue-600">正在聆听...</span>
          </div>
        </div>
      )}


    </div>
  );
};

// 🔥 使用React.memo优化InterviewContent组件
const InterviewContent = React.memo(InterviewContentComponent, (prevProps, nextProps) => {
  // 🔥 精确比较props，避免不必要的重渲染
  // 添加空值检查，防止undefined.length错误
  const prevMessages = prevProps.messages || [];
  const nextMessages = nextProps.messages || [];

  const messagesChanged = prevMessages.length !== nextMessages.length ||
    prevMessages.some((msg, index) => {
      const nextMsg = nextMessages[index];
      return !nextMsg ||
        msg.id !== nextMsg.id ||
        msg.content !== nextMsg.content ||
        msg.type !== nextMsg.type ||
        msg.timestamp !== nextMsg.timestamp;
    });

  const isListeningChanged = prevProps.isListening !== nextProps.isListening;

  const shouldUpdate = messagesChanged || isListeningChanged;

  if (!shouldUpdate) {
    console.log('✅ InterviewContent memo: Prevented unnecessary re-render');
  } else {
    console.log('🔄 InterviewContent re-render:', {
      messagesChanged,
      isListeningChanged,
      messageCount: nextProps.messages.length
    });
  }

  return !shouldUpdate;
});

// 设置displayName便于调试
InterviewContent.displayName = 'InterviewContent';

export default InterviewContent;
