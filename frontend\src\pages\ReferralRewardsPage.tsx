import React, { useState, useEffect } from 'react';
import useDocumentTitle from '../hooks/useDocumentTitle';
import { useToastContext } from '../contexts/ToastContext';
import InviteCodeGenerator from '../components/referral/InviteCodeGenerator';
import ReferralStats from '../components/referral/ReferralStats';
import ReferralHistory from '../components/referral/ReferralHistory';
import { referralApi } from '../lib/api/referral';

// 邀请码接口
interface ReferralCode {
  code: string;
  inviteLink: string;
  createdAt: string;
  isActive: boolean;
  hasInviter: boolean; // 是否已有邀请人
}

// 邀请统计接口
interface ReferralStats {
  totalInvited: number;
  successfulReferrals: number;
  totalRewards: number;
  pendingRewards: number;
}

// 邀请记录接口
interface ReferralRecord {
  id: string;
  invitedUserEmail: string;
  registeredAt: string;
  firstPaymentAt?: string;
  rewardAmount?: number;
  status: 'registered' | 'paid' | 'rewarded';
}

/**
 * 分享有礼主页面
 */
const ReferralRewardsPage: React.FC = () => {
  useDocumentTitle('分享有礼');

  const { showSuccess, showError, showInfo } = useToastContext();
  
  // 状态管理
  const [referralCode, setReferralCode] = useState<ReferralCode | null>(null);
  const [stats, setStats] = useState<ReferralStats | null>(null);
  const [records, setRecords] = useState<ReferralRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);

  /**
   * 加载邀请码
   */
  const loadReferralCode = async () => {
    try {
      const response = await referralApi.getReferralCode();
      if (response.success && response.data) {
        setReferralCode(response.data);
      } else {
        console.error('获取邀请码失败:', response.message);
        showToast(response.message || '获取邀请码失败', 'error');
        // 设置一个默认的空状态，但不阻止页面显示
        setReferralCode(null);
      }
    } catch (error) {
      console.error('加载邀请码失败:', error);
      showToast('网络错误，请稍后重试', 'error');
      // 设置一个默认的空状态，但不阻止页面显示
      setReferralCode(null);
    }
  };

  /**
   * 加载邀请统计
   */
  const loadStats = async () => {
    try {
      const response = await referralApi.getStats();
      if (response.success && response.data) {
        setStats(response.data);
      } else {
        showToast('获取统计数据失败', 'error');
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
      showToast('获取统计数据失败', 'error');
    }
  };

  /**
   * 加载邀请记录
   */
  const loadHistory = async (page: number = 1, append: boolean = false) => {
    try {
      if (page === 1) {
        setIsLoading(true);
      } else {
        setIsLoadingMore(true);
      }

      const response = await referralApi.getHistory({
        page,
        limit: 10,
        status: 'all'
      });

      if (response.success && response.data) {
        const newRecords = response.data.records;
        
        if (append) {
          setRecords(prev => [...prev, ...newRecords]);
        } else {
          setRecords(newRecords);
        }
        
        setHasMore(response.data.pagination.hasMore);
        setCurrentPage(page);
      } else {
        showToast('获取邀请记录失败', 'error');
      }
    } catch (error) {
      console.error('加载邀请记录失败:', error);
      showToast('获取邀请记录失败', 'error');
    } finally {
      setIsLoading(false);
      setIsLoadingMore(false);
    }
  };

  /**
   * 加载更多记录
   */
  const handleLoadMore = () => {
    if (!isLoadingMore && hasMore) {
      loadHistory(currentPage + 1, true);
    }
  };

  /**
   * 复制邀请码
   */
  const handleCopyCode = async (code: string) => {
    try {
      await navigator.clipboard.writeText(code);
      showSuccess('邀请码已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      showError('复制失败，请手动复制');
    }
  };

  /**
   * 复制邀请链接
   */
  const handleCopyLink = async (link: string) => {
    try {
      await navigator.clipboard.writeText(link);
      showSuccess('邀请链接已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
      showError('复制失败，请手动复制');
    }
  };

  /**
   * 分享到平台
   */
  const handleShare = (platform: string, link: string) => {
    const text = `邀请你加入面试君，一起提升面试技能！`;

    switch (platform) {
      case 'wechat':
        // 微信分享需要特殊处理，这里只是复制链接
        handleCopyLink(link);
        showInfo('链接已复制，请在微信中分享');
        break;
      case 'qq':
        // QQ分享
        window.open(`https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(link)}&title=${encodeURIComponent(text)}`);
        break;
      case 'weibo':
        // 微博分享
        window.open(`https://service.weibo.com/share/share.php?url=${encodeURIComponent(link)}&title=${encodeURIComponent(text)}`);
        break;
      case 'copy':
        handleCopyLink(link);
        break;
      default:
        handleCopyLink(link);
    }
  };

  /**
   * 设置邀请人邀请码
   */
  const handleSetInviterCode = async (inviterCode: string) => {
    try {
      const response = await referralApi.setInviterCode(inviterCode);
      if (response.success) {
        showToast('邀请人邀请码设置成功', 'success');
        // 刷新数据以更新统计
        await refreshData();
      } else {
        showToast(response.message || '设置失败', 'error');
      }
    } catch (error: any) {
      console.error('设置邀请人邀请码失败:', error);
      showToast(error.message || '设置失败，请稍后重试', 'error');
      throw error; // 重新抛出错误，让组件知道操作失败
    }
  };

  /**
   * 刷新数据
   */
  const refreshData = async () => {
    await Promise.all([
      loadReferralCode(),
      loadStats(),
      loadHistory(1, false)
    ]);
  };

  // 初始化加载
  useEffect(() => {
    refreshData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            分享有礼
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            邀请好友注册并完成首次充值，即可获得100面巾值奖励
          </p>
        </div>

        {/* 主要内容区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* 左侧：邀请码生成和统计 */}
          <div className="lg:col-span-2">
            <div className="space-y-6 lg:h-[750px] flex flex-col">
              {/* 邀请码生成组件 - 占2/5高度 */}
              <div className="lg:h-[300px] flex-shrink-0">
                <InviteCodeGenerator
                  referralCode={referralCode}
                  onCopyCode={handleCopyCode}
                  onCopyLink={handleCopyLink}
                  onShare={handleShare}
                  onSetInviterCode={handleSetInviterCode}
                  isLoading={isLoading}
                />
              </div>

              {/* 邀请记录组件 - 占3/5高度 */}
              <div className="flex-1">
                <ReferralHistory
                  records={records}
                  onLoadMore={handleLoadMore}
                  hasMore={hasMore}
                  isLoading={isLoading}
                  isLoadingMore={isLoadingMore}
                />
              </div>
            </div>
          </div>

          {/* 右侧：统计数据 */}
          <div className="lg:col-span-1">
            <div className="lg:h-[750px]">
              <ReferralStats
                stats={stats}
                isLoading={isLoading}
                onRefresh={refreshData}
              />
            </div>
          </div>
        </div>

        {/* 规则说明 */}
        <div className="mt-12 bg-white dark:bg-gray-800 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            活动规则
          </h3>
          <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
            <p>• 邀请好友通过您的专属邀请码注册面试君账户</p>
            <p>• 被邀请用户完成首次充值（包括兑换码兑换）后，您将获得100面巾值奖励</p>
            <p>• 每个邀请关系仅可获得一次奖励</p>
            <p>• 奖励将在被邀请用户完成首次充值后自动发放到您的账户</p>
            <p>• 面试君保留活动最终解释权</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReferralRewardsPage;
