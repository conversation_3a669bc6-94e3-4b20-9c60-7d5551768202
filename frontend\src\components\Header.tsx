import React, { useState, useRef, useEffect } from 'react';
import { Bell, Search, LogOut, User, Settings, LogIn, ShoppingBag, FileText } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import useAuthStore from '../stores/authStore';

interface HeaderProps {
  greeting?: string;
}

const Header: React.FC<HeaderProps> = ({ greeting }) => {
  const navigate = useNavigate();
  const { isAuthenticated, user, logout } = useAuthStore();
  const [showUserMenu, setShowUserMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  // 处理登出
  const handleLogout = () => {
    setShowUserMenu(false);
    logout();
    navigate('/ai-login');
  };

  // 处理登录
  const handleLogin = () => {
    setShowUserMenu(false);
    navigate('/ai-login');
  };

  // 处理点击头像
  const toggleUserMenu = () => {
    setShowUserMenu(!showUserMenu);
  };

  // 处理点击其他地方关闭菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 处理菜单项点击
  const handleMenuItemClick = (action: string) => {
    switch(action) {
      case 'profile':
        navigate('/profile');
        break;
      case 'orders':
        navigate('/orders');
        break;
      case 'usage-records':
        navigate('/usage-records');
        break;
      case 'settings':
        navigate('/settings');
        break;
      default:
        break;
    }
    setShowUserMenu(false);
  };

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-100 dark:border-gray-700 py-4 px-8 flex items-center justify-between transition-colors">
      <div className="flex items-center gap-4">
        {greeting && (
          <h1 className="text-xl font-bold text-gray-800 dark:text-white">
            {greeting}
          </h1>
        )}
      </div>

      <div className="flex items-center gap-6">
        <div className="relative">
          <input
            type="text"
            placeholder="搜索面试技巧..."
            className="w-64 px-4 py-2 pl-10 bg-gray-50 dark:bg-gray-700 rounded-xl text-sm text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-sky-500 dark:focus:ring-sky-400 transition-colors"
          />
          <Search className="w-4 h-4 text-gray-400 dark:text-gray-500 absolute left-3 top-1/2 transform -translate-y-1/2" />
        </div>

        {/* 通知图标 - 始终显示 */}
        <button className="relative p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors">
          <Bell className="w-5 h-5" />
          <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 dark:bg-red-400 rounded-full"></span>
        </button>

        {/* 用户头像 - 始终显示 */}
        <div className="relative" ref={menuRef}>
          <button
            onClick={toggleUserMenu}
            className="h-10 w-10 rounded-xl bg-gradient-to-br from-violet-500 to-purple-500 flex items-center justify-center text-white font-medium shadow-lg hover:shadow-xl transition-all"
            title="点击显示菜单"
          >
            {isAuthenticated
              ? (user?.name?.[0]?.toUpperCase() || user?.email?.[0]?.toUpperCase() || 'U')
              : 'A'
            }
          </button>

          {/* 用户下拉菜单 */}
          {showUserMenu && (
            <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-xl py-1 z-10 border border-gray-100 dark:border-gray-600 transition-colors">
              {isAuthenticated ? (
                <>
                  <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-600">
                    <p className="text-sm font-medium text-gray-700 dark:text-white truncate">{user?.name || '用户'}</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 truncate">{user?.email}</p>
                  </div>

                  <button
                    onClick={() => handleMenuItemClick('profile')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <User className="w-4 h-4 mr-2" />
                    个人中心
                  </button>

                  <button
                    onClick={() => handleMenuItemClick('orders')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <ShoppingBag className="w-4 h-4 mr-2" />
                    我的订单
                  </button>

                  <button
                    onClick={() => handleMenuItemClick('usage-records')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    消耗记录
                  </button>

                  <button
                    onClick={() => handleMenuItemClick('settings')}
                    className="flex items-center w-full px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <Settings className="w-4 h-4 mr-2" />
                    设置
                  </button>

                  <div className="border-t border-gray-100 dark:border-gray-600 my-1"></div>

                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    退出登录
                  </button>
                </>
              ) : (
                <>
                  <div className="px-4 py-2 border-b border-gray-100 dark:border-gray-600">
                    <p className="text-sm font-medium text-gray-700 dark:text-white">未登录状态</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">请登录以使用完整功能</p>
                  </div>

                  <button
                    onClick={handleLogin}
                    className="flex items-center w-full px-4 py-2 text-sm text-blue-600 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  >
                    <LogIn className="w-4 h-4 mr-2" />
                    登录
                  </button>
                </>
              )}
            </div>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;