import prisma from '../lib/prisma';
import { nanoid } from 'nanoid';

// 邀请码配置
const REFERRAL_CODE_LENGTH = 8;
const REFERRAL_CODE_ALPHABET = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';

// 邀请统计接口
export interface ReferralStats {
  totalInvited: number;           // 总邀请人数
  successfulReferrals: number;    // 成功充值人数
  totalRewards: number;           // 累计奖励面巾值
  pendingRewards: number;         // 待发放奖励
}

// 邀请码接口
export interface ReferralCode {
  code: string;                   // 邀请码
  inviteLink: string;            // 邀请链接
  createdAt: string;             // 创建时间
  isActive: boolean;             // 是否有效
}

// 邀请记录接口
export interface ReferralRecord {
  id: string;
  invitedUserEmail: string;       // 被邀请用户邮箱(脱敏)
  registeredAt: string;           // 注册时间
  firstPaymentAt?: string;        // 首次充值时间
  rewardAmount?: number;          // 奖励金额
  status: 'registered' | 'paid' | 'rewarded';
}

// 分页选项
export interface PaginationOptions {
  page?: number;
  limit?: number;
  status?: 'all' | 'registered' | 'paid' | 'rewarded';
}

// 分页结果
export interface PaginatedReferralRecords {
  records: ReferralRecord[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasMore: boolean;
  };
}

/**
 * 邀请服务类
 */
export class ReferralService {
  
  /**
   * 生成邀请码
   */
  private generateReferralCode(): string {
    return nanoid(REFERRAL_CODE_LENGTH).toUpperCase();
  }

  /**
   * 生成邀请链接
   */
  private generateInviteLink(code: string): string {
    const baseUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    return `${baseUrl}/register?invite=${code}`;
  }

  /**
   * 邮箱脱敏处理
   */
  private maskEmail(email: string): string {
    const [localPart, domain] = email.split('@');
    if (localPart.length <= 6) {
      // 短邮箱：aaa***<EMAIL>
      const start = localPart.substring(0, 3);
      const end = localPart.substring(localPart.length - 2);
      return `${start}***${end}@${domain}`;
    } else {
      // 长邮箱：aaa***<EMAIL>
      const start = localPart.substring(0, 3);
      const end = localPart.substring(localPart.length - 2);
      return `${start}***${end}@${domain}`;
    }
  }

  /**
   * 获取或创建用户邀请码
   */
  async getOrCreateReferralCode(userId: string): Promise<ReferralCode> {
    try {
      // 先查找是否已有邀请码
      let referralCode = await prisma.referralCode.findFirst({
        where: { 
          userId,
          isActive: true 
        },
        orderBy: { createdAt: 'desc' }
      });

      // 如果没有，创建新的邀请码
      if (!referralCode) {
        let code: string;
        let attempts = 0;
        const maxAttempts = 10;

        // 确保邀请码唯一性
        do {
          code = this.generateReferralCode();
          const existing = await prisma.referralCode.findUnique({
            where: { code }
          });
          if (!existing) break;
          attempts++;
        } while (attempts < maxAttempts);

        if (attempts >= maxAttempts) {
          throw new Error('无法生成唯一邀请码，请稍后重试');
        }

        referralCode = await prisma.referralCode.create({
          data: {
            userId,
            code: code!,
            isActive: true
          }
        });
      }

      return {
        code: referralCode.code,
        inviteLink: this.generateInviteLink(referralCode.code),
        createdAt: referralCode.createdAt.toISOString(),
        isActive: referralCode.isActive
      };

    } catch (error) {
      console.error('获取或创建邀请码失败:', error);
      throw new Error('邀请码生成失败');
    }
  }

  /**
   * 验证邀请码有效性
   */
  async validateReferralCode(code: string): Promise<boolean> {
    try {
      const referralCode = await prisma.referralCode.findUnique({
        where: { code: code.trim().toUpperCase() }
      });

      return !!(referralCode &&
                referralCode.isActive &&
                (!referralCode.expiresAt || new Date() < referralCode.expiresAt));

    } catch (error) {
      console.error('验证邀请码失败:', error);
      return false;
    }
  }

  /**
   * 检查用户是否已有邀请关系
   */
  async checkExistingReferralRelation(userId: string): Promise<boolean> {
    try {
      const existingRelation = await prisma.referralRelation.findUnique({
        where: { inviteeId: userId }
      });

      return !!existingRelation;

    } catch (error) {
      console.error('检查邀请关系失败:', error);
      return false;
    }
  }

  /**
   * 创建邀请关系
   */
  async createReferralRelation(inviterCode: string, inviteeUserId: string): Promise<void> {
    try {
      // 查找邀请码对应的邀请者
      const referralCode = await prisma.referralCode.findUnique({
        where: { code: inviterCode.trim().toUpperCase() },
        include: { user: true }
      });

      if (!referralCode || !referralCode.isActive) {
        throw new Error('邀请码无效或已失效');
      }

      // 检查是否自己邀请自己
      if (referralCode.userId === inviteeUserId) {
        throw new Error('不能邀请自己');
      }

      // 检查被邀请用户是否已有邀请关系
      const existingRelation = await prisma.referralRelation.findUnique({
        where: { inviteeId: inviteeUserId }
      });

      if (existingRelation) {
        console.log('用户已有邀请关系，跳过创建');
        return;
      }

      // 创建邀请关系
      await prisma.referralRelation.create({
        data: {
          inviterId: referralCode.userId,
          inviteeId: inviteeUserId,
          referralCode: inviterCode.trim().toUpperCase()
        }
      });

      console.log(`✅ 邀请关系创建成功: ${referralCode.userId} -> ${inviteeUserId}`);

    } catch (error) {
      console.error('创建邀请关系失败:', error);
      throw error;
    }
  }

  /**
   * 获取邀请统计
   */
  async getReferralStats(userId: string): Promise<ReferralStats> {
    try {
      // 获取总邀请人数
      const totalInvited = await prisma.referralRelation.count({
        where: { inviterId: userId }
      });

      // 获取成功充值人数（有首次充值时间的）
      const successfulReferrals = await prisma.referralRelation.count({
        where: { 
          inviterId: userId,
          firstPaymentAt: { not: null }
        }
      });

      // 获取累计奖励和待发放奖励
      const rewardStats = await prisma.referralReward.aggregate({
        where: { inviterId: userId },
        _sum: { rewardAmount: true },
        _count: { id: true }
      });

      const processedRewards = await prisma.referralReward.aggregate({
        where: { 
          inviterId: userId,
          status: 'COMPLETED'
        },
        _sum: { rewardAmount: true }
      });

      const totalRewards = processedRewards._sum.rewardAmount || 0;
      const pendingRewards = (rewardStats._sum.rewardAmount || 0) - totalRewards;

      return {
        totalInvited,
        successfulReferrals,
        totalRewards,
        pendingRewards
      };

    } catch (error) {
      console.error('获取邀请统计失败:', error);
      throw new Error('获取统计数据失败');
    }
  }

  /**
   * 获取邀请记录
   */
  async getReferralHistory(userId: string, options: PaginationOptions = {}): Promise<PaginatedReferralRecords> {
    try {
      const { page = 1, limit = 10, status = 'all' } = options;
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = { inviterId: userId };
      
      if (status === 'registered') {
        where.firstPaymentAt = null;
        where.rewardGranted = false;
      } else if (status === 'paid') {
        where.firstPaymentAt = { not: null };
        where.rewardGranted = false;
      } else if (status === 'rewarded') {
        where.rewardGranted = true;
      }

      // 获取记录和总数
      const [relations, total] = await Promise.all([
        prisma.referralRelation.findMany({
          where,
          include: {
            invitee: { select: { email: true } },
            rewards: { select: { rewardAmount: true, status: true } }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.referralRelation.count({ where })
      ]);

      // 转换为前端格式
      const records: ReferralRecord[] = relations.map(relation => {
        let status: 'registered' | 'paid' | 'rewarded' = 'registered';
        if (relation.rewardGranted) {
          status = 'rewarded';
        } else if (relation.firstPaymentAt) {
          status = 'paid';
        }

        const completedReward = relation.rewards.find(r => r.status === 'COMPLETED');

        return {
          id: relation.id,
          invitedUserEmail: this.maskEmail(relation.invitee.email),
          registeredAt: relation.createdAt.toISOString(),
          firstPaymentAt: relation.firstPaymentAt?.toISOString(),
          rewardAmount: completedReward?.rewardAmount,
          status
        };
      });

      return {
        records,
        pagination: {
          page,
          limit,
          total,
          hasMore: skip + limit < total
        }
      };

    } catch (error) {
      console.error('获取邀请记录失败:', error);
      throw new Error('获取邀请记录失败');
    }
  }
}

// 导出单例实例
export const referralService = new ReferralService();
